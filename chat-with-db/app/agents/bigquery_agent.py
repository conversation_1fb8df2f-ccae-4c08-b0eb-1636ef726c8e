import os
import json
import pandas as pd
from datetime import datetime, date
from decimal import Decimal
from typing import Dict, Any, List, Optional, AsyncGenerator

from vanna.openai.openai_chat import OpenAI_Chat
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore

from .base_agent import DatabaseChatAgent


def serialize_value(value):
    """Convert non-JSON serializable values to JSON serializable ones"""
    if isinstance(value, (datetime, date)):
        return value.isoformat()
    elif isinstance(value, Decimal):
        return float(value)
    elif value is None:
        return None
    else:
        return str(value)


class VannaBigQuery(ChromaDB_VectorStore, OpenAI_Chat):
    """
    Custom Vanna class that combines ChromaDB for vector storage and OpenAI for LLM
    """
    def __init__(self, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)
        OpenAI_Chat.__init__(self, config=config)


class BigQueryChatAgent(DatabaseChatAgent):
    """
    Implementation of DatabaseChatAgent for Google BigQuery using Vanna AI with OpenAI + ChromaDB
    """

    def __init__(
        self,
        project_id: str,
        vanna_model_name: str = "bigquery-model",
        vanna_api_key: str = None,
        cred_file_path: Optional[str] = None,
        model: str = "gpt-4",  # OpenAI model to use
        api_key: str = None,   # Alternative parameter name for API key
    ):
        """
        Initialize the BigQuery chat agent with Vanna AI

        Args:
            project_id: Google Cloud project ID
            vanna_model_name: Name for the Vanna model (used for ChromaDB collection)
            vanna_api_key: OpenAI API key for LLM
            cred_file_path: Path to Google Cloud credentials JSON file
            model: OpenAI model to use (e.g., 'gpt-4', 'gpt-3.5-turbo')
            api_key: Alternative parameter name for API key
        """
        self.project_id = project_id
        self.cred_file_path = cred_file_path
        self.vanna_model_name = vanna_model_name

        # Use api_key parameter if provided, otherwise fall back to vanna_api_key or environment
        self.vanna_api_key = api_key or vanna_api_key or os.environ.get("OPENAI_API_KEY")
        self.model = model
        self.is_connected = False
        self.is_trained = False

        # Initialize Vanna with OpenAI and ChromaDB
        self.vn = VannaBigQuery(config={
            'api_key': self.vanna_api_key,
            'model': self.model,
            'path': f'./chroma_db_{vanna_model_name}',  # Local ChromaDB path
        })

    def connect(self) -> bool:
        """
        Connect to BigQuery using Vanna's built-in connection

        Returns:
            bool: True if connection was successful, False otherwise
        """
        try:
            # Use Vanna's built-in BigQuery connection
            if self.cred_file_path:
                self.vn.connect_to_bigquery(
                    project_id=self.project_id,
                    cred_file_path=self.cred_file_path
                )
            else:
                # Use Application Default Credentials
                self.vn.connect_to_bigquery(project_id=self.project_id)

            # Test connection by running a simple query
            test_result = self.vn.run_sql("SELECT 1 as test")
            if test_result is not None:
                self.is_connected = True
                print(f"✅ Successfully connected to BigQuery project: {self.project_id}")
                return True
            else:
                self.is_connected = False
                return False
        except Exception as e:
            print(f"❌ Error connecting to BigQuery: {e}")
            self.is_connected = False
            return False

    def train(self, additional_context: Optional[str] = None) -> bool:
        """
        Train the agent on the BigQuery schema using Vanna AI

        Args:
            additional_context: Optional additional documentation or context to train on

        Returns:
            bool: True if training was successful, False otherwise
        """
        if not self.is_connected:
            print("❌ Not connected to BigQuery. Call connect() first.")
            return False

        try:
            print("🔄 Training Vanna AI on BigQuery schema...")

            # Fetch schema information from BigQuery using Vanna
            # First, let's check what datasets exist
            datasets_df = self.vn.run_sql(f"""
                SELECT schema_name
                FROM `{self.project_id}.INFORMATION_SCHEMA.SCHEMATA`
                WHERE schema_name NOT IN ('INFORMATION_SCHEMA', 'information_schema')
            """)

            if datasets_df is None or datasets_df.empty:
                print("❌ No datasets found in the project")
                print("💡 Creating a sample dataset for demonstration...")

                # Create a simple sample table for demonstration
                sample_ddl = f"""
                CREATE TABLE `{self.project_id}.sample_dataset.sample_table` (
                    id INT64,
                    name STRING,
                    created_at TIMESTAMP
                );
                """
                self.vn.train(ddl=sample_ddl)
                print("✅ Trained on sample schema")

                self.is_trained = True
                return True

            print(f"📊 Found datasets: {list(datasets_df['schema_name'])}")

            # Now fetch detailed schema information for each dataset
            all_schema_data = []

            for dataset in datasets_df['schema_name']:
                try:
                    print(f"🔍 Fetching schema for dataset: {dataset}")
                    schema_query = f"""
                        SELECT
                            table_catalog,
                            table_schema,
                            table_name,
                            column_name,
                            data_type,
                            is_nullable,
                            column_default
                        FROM
                            `{self.project_id}.{dataset}.INFORMATION_SCHEMA.COLUMNS`
                        ORDER BY table_name, ordinal_position
                    """

                    dataset_schema = self.vn.run_sql(schema_query)
                    if dataset_schema is not None and not dataset_schema.empty:
                        all_schema_data.append(dataset_schema)
                        print(f"✅ Found {len(dataset_schema)} columns in dataset {dataset}")
                    else:
                        print(f"⚠️ No tables found in dataset {dataset}")

                except Exception as e:
                    print(f"⚠️ Could not fetch schema for dataset {dataset}: {e}")
                    continue

            if not all_schema_data:
                print("❌ No schema information found in any dataset")
                return False

            # Combine all schema data
            df_schema = pd.concat(all_schema_data, ignore_index=True)

            if df_schema is None or df_schema.empty:
                print("❌ No schema information found")
                return False

            print(f"📊 Found {len(df_schema)} columns across {df_schema['table_name'].nunique()} tables")

            # Train on DDL statements instead of using training plan
            # Get table information and create DDL statements
            tables = df_schema.groupby(['table_schema', 'table_name'])

            for (schema, table), group in tables:
                # Create DDL statement for each table
                columns_info = []
                for _, row in group.iterrows():
                    col_def = f"{row['column_name']} {row['data_type']}"
                    if row['is_nullable'] == 'NO':
                        col_def += " NOT NULL"
                    if pd.notna(row['column_default']):
                        col_def += f" DEFAULT {row['column_default']}"
                    columns_info.append(col_def)

                ddl = f"CREATE TABLE `{self.project_id}.{schema}.{table}` (\n    " + ",\n    ".join(columns_info) + "\n);"

                # Train Vanna on this DDL
                self.vn.train(ddl=ddl)
                print(f"✅ Trained on table: {schema}.{table}")

            print("✅ Successfully trained on database schema")

            # Add additional context if provided
            if additional_context:
                self.vn.train(documentation=additional_context)
                print("✅ Successfully added additional context")

            # Add some common business context
            business_context = """
            This is a BigQuery data warehouse containing information about:
            - Business analytics and reporting data
            - Time-series data and metrics
            - Various business operations and KPIs
            - Data from multiple sources and systems
            """
            self.vn.train(documentation=business_context)

            self.is_trained = True
            print("🎉 Training completed successfully!")
            return True

        except Exception as e:
            print(f"❌ Error training on BigQuery schema: {e}")
            return False

    async def generate_sql(self, question: str) -> str:
        """
        Generate SQL for the given natural language question using Vanna AI

        Args:
            question: The natural language question to convert to SQL

        Returns:
            str: The generated SQL query
        """
        if not self.is_trained:
            raise ValueError("❌ Agent not trained. Call train() first.")

        try:
            print(f"🤖 Generating SQL for: {question}")

            # Use Vanna's AI to generate SQL
            sql = self.vn.generate_sql(question)

            print(f"✅ Generated SQL: {sql}")
            return sql

        except Exception as e:
            print(f"❌ Error generating SQL: {e}")
            # Fallback to a simple query if AI generation fails
            return "SELECT 'Error generating SQL. Please try a different question.' as message"

    async def execute_sql(self, sql: str) -> Dict[str, Any]:
        """
        Execute the given SQL query on BigQuery using Vanna

        Args:
            sql: The SQL query to execute

        Returns:
            Dict[str, Any]: The query results
        """
        if not self.is_connected:
            raise ValueError("❌ Not connected to BigQuery. Call connect() first.")

        try:
            print(f"🔄 Executing SQL: {sql}")

            # Use Vanna to execute the SQL
            results_df = self.vn.run_sql(sql)

            if results_df is None:
                return {"error": "Query returned no results"}

            # Convert DataFrame to dict for JSON serialization
            if isinstance(results_df, pd.DataFrame):
                # Apply serialization to all values
                serialized_df = results_df.applymap(serialize_value)
                results = serialized_df.to_dict(orient="records")
                columns = list(results_df.columns)
            else:
                results = [{"result": serialize_value(results_df)}]
                columns = ["result"]

            print(f"✅ Query executed successfully, returned {len(results)} rows")

            return {
                "results": results,
                "columns": columns,
                "row_count": len(results)
            }
        except Exception as e:
            print(f"❌ Error executing SQL: {e}")
            return {"error": str(e)}

    async def answer_query(self, question: str) -> Dict[str, Any]:
        """
        Generate SQL for the question, execute it, and return an answer using Vanna AI

        Args:
            question: The natural language question to answer

        Returns:
            Dict[str, Any]: The answer with SQL, results, and explanation
        """
        if not self.is_trained:
            return {"error": "❌ Agent not trained. Call train() first."}

        try:
            print(f"🎯 Answering query: {question}")

            # Generate SQL using Vanna AI
            sql = await self.generate_sql(question)

            # Execute SQL
            results = await self.execute_sql(sql)

            # Generate explanation using Vanna AI
            if "error" in results:
                explanation = f"There was an error executing the query: {results['error']}"
            else:
                try:
                    # Convert results back to DataFrame for Vanna's summary generation
                    if results.get('results'):
                        results_df = pd.DataFrame(results['results'])
                        explanation = self.vn.generate_summary(question, results_df)
                    else:
                        explanation = "The query executed successfully but returned no results."
                except Exception as e:
                    print(f"⚠️ Could not generate AI summary, using fallback: {e}")
                    row_count = results.get('row_count', 0)
                    explanation = f"I executed the SQL query and found {row_count} result(s). The query searched the BigQuery data warehouse based on your question: '{question}'"

            print(f"✅ Query answered successfully")

            return {
                "question": question,
                "sql": sql,
                "results": results,
                "explanation": explanation
            }
        except Exception as e:
            print(f"❌ Error answering query: {e}")
            return {"error": str(e)}

    async def answer_query_stream(self, question: str) -> AsyncGenerator[str, None]:
        """
        Stream the answer to a question using Vanna AI

        Args:
            question: The natural language question to answer

        Yields:
            str: Chunks of the answer as they are generated
        """
        if not self.is_trained:
            yield json.dumps({"error": "❌ Agent not trained. Call train() first."})
            return

        try:
            # First yield a message that we're generating SQL
            yield json.dumps({"status": "generating_sql"})

            # Generate SQL using Vanna AI
            sql = await self.generate_sql(question)
            yield json.dumps({"sql": sql})

            # Execute SQL
            yield json.dumps({"status": "executing_sql"})
            results = await self.execute_sql(sql)
            yield json.dumps({"results": results})

            # Generate explanation using Vanna AI
            yield json.dumps({"status": "generating_explanation"})

            if "error" in results:
                explanation = f"There was an error executing the query: {results['error']}"
            else:
                try:
                    # Convert results back to DataFrame for Vanna's summary generation
                    if results.get('results'):
                        results_df = pd.DataFrame(results['results'])
                        explanation = self.vn.generate_summary(question, results_df)
                    else:
                        explanation = "The query executed successfully but returned no results."
                except Exception as e:
                    print(f"⚠️ Could not generate AI summary, using fallback: {e}")
                    row_count = results.get('row_count', 0)
                    explanation = f"I executed the SQL query and found {row_count} result(s). The query searched the BigQuery data warehouse based on your question: '{question}'"

            # Yield the explanation in chunks for a streaming effect
            for i in range(0, len(explanation), 20):
                chunk = explanation[i:i+20]
                yield json.dumps({"explanation_chunk": chunk})

            # Final complete response
            yield json.dumps({
                "complete": True,
                "question": question,
                "sql": sql,
                "results": results,
                "explanation": explanation
            })
        except Exception as e:
            print(f"❌ Error in streaming answer: {e}")
            yield json.dumps({"error": str(e)})