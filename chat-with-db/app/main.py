import os
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from dotenv import load_dotenv

from .routers import chat_router

# Load environment variables from .env file
load_dotenv()

# Create FastAPI app
app = FastAPI(
    title="Chat with DB AI Agent",
    description="AI agent for natural language database queries using Google ADK and Vanna AI",
    version="0.1.0",
)

# Configure CORS based on environment
allowed_origins = ["*"]  # Default for development
if os.getenv("ENVIRONMENT") == "production":
    # In production, use specific origins from environment variable
    origins_env = os.getenv("ALLOWED_ORIGINS", "")
    if origins_env:
        allowed_origins = [origin.strip() for origin in origins_env.split(",")]
    else:
        # Fallback to common patterns if not specified
        allowed_origins = [
            "https://*.vercel.app",
            "https://*.netlify.app",
            "https://*.run.app",
            "https://localhost:3000",
            "http://localhost:3000"
        ]

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# Include routers
app.include_router(chat_router)


@app.get("/")
async def root():
    """
    Root endpoint

    Returns:
        dict: Basic information about the API
    """
    return {
        "message": "Chat with DB AI Agent API",
        "docs_url": "/docs",
        "redoc_url": "/redoc",
    }


@app.get("/health")
async def health_check():
    """
    Health check endpoint

    Returns:
        dict: Health status
    """
    return {"status": "healthy"}

@app.post("/reset-chromadb")
async def reset_chromadb():
    """
    Reset ChromaDB databases to fix schema issues

    Returns:
        dict: Reset status
    """
    try:
        import shutil

        directories = [
            "chroma_db_bigquery-model",
            "chroma_db_postgres-model"
        ]

        for directory in directories:
            if os.path.exists(directory):
                shutil.rmtree(directory)
            os.makedirs(directory, exist_ok=True)

        return {"status": "success", "message": "ChromaDB reset completed"}
    except Exception as e:
        return {"status": "error", "message": str(e)}


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """
    Global exception handler

    Args:
        request: The request that caused the exception
        exc: The exception

    Returns:
        JSONResponse: Error response
    """
    # Log the error for debugging
    import logging
    logging.error(f"Unhandled exception: {exc}", exc_info=True)

    # Don't expose internal errors in production
    if os.getenv("ENVIRONMENT") == "production":
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error"},
        )
    else:
        return JSONResponse(
            status_code=500,
            content={"detail": str(exc)},
        )
