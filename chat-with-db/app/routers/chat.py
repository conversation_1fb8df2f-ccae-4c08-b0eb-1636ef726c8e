import json
from typing import Dict, Any, Optional, List
from fastapi import APIRout<PERSON>, Depends, HTTPException, Request, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from sse_starlette.sse import EventSourceResponse
import asyncio

from ..agents import DatabaseChatAgent, AgentFactory


# Define request and response models
class ChatRequest(BaseModel):
    question: str
    db_type: str = "postgres"  # Default to postgres
    stream: bool = False


class ChatResponse(BaseModel):
    question: str
    sql: str
    results: Dict[str, Any]
    explanation: str


# Create router
router = APIRouter(
    prefix="/api/chat",
    tags=["chat"],
    responses={404: {"description": "Not found"}},
)


# Create a global agent instance to reuse
_postgres_agent = None
_bigquery_agent = None

# Dependency to get the appropriate database agent
def get_db_agent_factory(db_type: str):
    """
    Factory function to create a dependency for the specified database type

    Args:
        db_type: Type of database ('bigquery' or 'postgres')

    Returns:
        Function that returns the appropriate database agent
    """
    async def get_agent() -> DatabaseChatAgent:
        global _postgres_agent, _bigquery_agent

        try:
            # Use cached agent if available
            if db_type.lower() == "postgres" and _postgres_agent is not None:
                return _postgres_agent
            elif db_type.lower() == "bigquery" and _bigquery_agent is not None:
                return _bigquery_agent

            print(f"Creating new {db_type} agent...")

            # Create agent from environment variables
            agent = AgentFactory.create_agent_from_env(db_type)

            # Connect to the database
            print(f"Connecting to {db_type} database...")
            if not agent.connect():
                raise HTTPException(status_code=500, detail=f"Failed to connect to {db_type} database")

            # Train the agent on the database schema
            print(f"Training agent on {db_type} schema...")
            if not agent.train():
                raise HTTPException(status_code=500, detail=f"Failed to train agent on {db_type} schema")

            # Cache the agent
            if db_type.lower() == "postgres":
                _postgres_agent = agent
            elif db_type.lower() == "bigquery":
                _bigquery_agent = agent

            print(f"{db_type} agent ready!")
            return agent
        except Exception as e:
            print(f"Error creating {db_type} agent: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))

    return get_agent


@router.post("/ask", response_model=ChatResponse)
async def ask_question(request: ChatRequest):
    """
    Ask a question to the database chat agent

    Args:
        request: Chat request containing the question and database type

    Returns:
        ChatResponse: The answer to the question
    """
    # Get the appropriate agent based on db_type
    agent = await get_db_agent_factory(request.db_type)()

    if request.stream:
        return EventSourceResponse(agent.answer_query_stream(request.question))
    else:
        response = await agent.answer_query(request.question)
        if "error" in response:
            raise HTTPException(status_code=500, detail=response["error"])
        return response


@router.get("/ask/stream")
async def ask_question_stream_get(
    question: str,
    db_type: str = "postgres"
):
    """
    Ask a question to the database chat agent and stream the response (GET method)

    Args:
        question: The natural language question to answer
        db_type: Type of database ('bigquery' or 'postgres')

    Returns:
        StreamingResponse: Streaming response with the answer
    """
    agent = await get_db_agent_factory(db_type)()
    return EventSourceResponse(agent.answer_query_stream(question))

@router.post("/ask/stream")
async def ask_question_stream_post(request: ChatRequest):
    """
    Ask a question to the database chat agent and stream the response (POST method)

    Args:
        request: Chat request containing the question and database type

    Returns:
        StreamingResponse: Streaming response with the answer
    """
    agent = await get_db_agent_factory(request.db_type)()
    return EventSourceResponse(agent.answer_query_stream(request.question))


@router.post("/generate-sql")
async def generate_sql(request: ChatRequest):
    """
    Generate SQL for a natural language question

    Args:
        request: Chat request containing the question and database type

    Returns:
        Dict[str, str]: The generated SQL
    """
    agent = await get_db_agent_factory(request.db_type)()
    sql = await agent.generate_sql(request.question)
    return {"sql": sql}


@router.post("/execute-sql")
async def execute_sql(sql: str, db_type: str = "postgres"):
    """
    Execute SQL on the database

    Args:
        sql: SQL query to execute
        db_type: Type of database ('bigquery' or 'postgres')

    Returns:
        Dict[str, Any]: The query results
    """
    agent = await get_db_agent_factory(db_type)()
    results = await agent.execute_sql(sql)
    return results
