# Cloud Run Deployment Guide

This guide will help you deploy the Chat with DB AI Agent to Google Cloud Run.

## Prerequisites

1. **Google Cloud Project**: You need a Google Cloud project with billing enabled
2. **Google Cloud CLI**: Install and configure `gcloud` CLI
3. **Docker**: Install Docker on your local machine
4. **API Keys**: OpenAI API key for Vanna AI
5. **Database Access**: PostgreSQL or BigQuery database credentials

## Quick Deployment

### 1. Prepare Environment

```bash
# Clone and navigate to the project
cd chat-with-db

# Copy and configure environment variables
cp .env.production .env
# Edit .env with your actual values
```

### 2. Set Up Google Cloud

```bash
# Login to Google Cloud
gcloud auth login

# Set your project ID
gcloud config set project ais-prod-440309

# Enable required APIs
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
gcloud services enable bigquery.googleapis.com
```

### 3. Create Service Account (for BigQuery access)

```bash
# Create service account
gcloud iam service-accounts create chat-with-db-ai \
    --display-name="Chat with DB AI Service Account"

# Grant BigQuery permissions
gcloud projects add-iam-policy-binding ais-prod-440309 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/bigquery.dataViewer"

gcloud projects add-iam-policy-binding ais-prod-440309 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/bigquery.jobUser"
```

### 4. Deploy to Cloud Run

```bash
# Run the deployment script
./deploy-cloud-run.sh
```

## Manual Deployment Steps

If you prefer manual deployment:

### 1. Build and Push Docker Image

```bash
# Set variables
PROJECT_ID="ais-prod-440309"
SERVICE_NAME="chat-with-db-ai"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

# Build image
docker build -t ${IMAGE_NAME} .

# Push to registry
docker push ${IMAGE_NAME}
```

### 2. Deploy to Cloud Run

```bash
gcloud run deploy chat-with-db-ai \
    --image gcr.io/ais-prod-440309/chat-with-db-ai \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated \
    --memory 2Gi \
    --cpu 2 \
    --timeout 3600 \
    --concurrency 80 \
    --max-instances 10 \
    --service-account "<EMAIL>"
```

### 3. Set Environment Variables

```bash
gcloud run services update chat-with-db-ai \
    --region us-central1 \
    --set-env-vars "OPENAI_API_KEY=your_key_here,GOOGLE_CLOUD_PROJECT=ais-prod-440309"
```

## Configuration

### Environment Variables

Set these in Cloud Run console or via gcloud:

- `OPENAI_API_KEY`: Your OpenAI API key
- `GOOGLE_CLOUD_PROJECT`: Your Google Cloud project ID
- `PGHOST`, `PGUSER`, `PGPASSWORD`, `PGDATABASE`: PostgreSQL credentials (if using)

### Security

1. **Service Account**: Use dedicated service account with minimal permissions
2. **Secrets**: Store sensitive data in Google Secret Manager
3. **CORS**: Configure allowed origins for production
4. **Authentication**: Consider adding authentication for production use

## Testing

After deployment, test your service:

```bash
# Get service URL
SERVICE_URL=$(gcloud run services describe chat-with-db-ai --region=us-central1 --format='value(status.url)')

# Test health endpoint
curl ${SERVICE_URL}/health

# Test API
curl -X POST ${SERVICE_URL}/api/chat/ask \
  -H "Content-Type: application/json" \
  -d '{"question": "How many tables are in the database?", "db_type": "bigquery"}'
```

## Monitoring

1. **Cloud Run Metrics**: Monitor in Google Cloud Console
2. **Logs**: View logs in Cloud Logging
3. **Alerts**: Set up alerting for errors and performance

## Troubleshooting

### Common Issues

1. **Memory Issues**: Increase memory allocation if needed
2. **Timeout**: Increase timeout for long-running queries
3. **Permissions**: Ensure service account has proper BigQuery permissions
4. **Cold Starts**: Consider min-instances for better performance

### Logs

```bash
# View logs
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=chat-with-db-ai" --limit 50
```

## Cost Optimization

1. **Min Instances**: Set to 0 for development, 1+ for production
2. **CPU Allocation**: Use CPU allocation only during requests
3. **Memory**: Right-size memory based on usage
4. **Concurrency**: Optimize concurrency settings

## Updates

To update the service:

```bash
# Rebuild and redeploy
./deploy-cloud-run.sh
```

Or use Cloud Build for CI/CD integration.
