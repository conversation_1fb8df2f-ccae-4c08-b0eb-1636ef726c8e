# Chat with DB AI Agent

An AI agent for natural language database queries using Vanna AI with OpenAI and ChromaDB for vector storage.

## Features

- 🤖 **Natural language to SQL conversion** using Vanna AI
- 🗄️ **Multi-database support**: BigQuery and PostgreSQL
- 📡 **Streaming responses** for real-time feedback
- ⚡ **FastAPI backend** with comprehensive API
- 🐳 **Docker support** for easy deployment
- ☁️ **Cloud Run ready** with production configurations
- 🔒 **Security features** including CORS configuration and error handling

## Prerequisites

- Python 3.11+
- PostgreSQL or BigQuery database access
- OpenAI API key for Vanna AI
- Google Cloud Project (for BigQuery and Cloud Run deployment)

## Quick Start

### Automated Setup

```bash
# Run the setup script
./setup.sh
```

### Manual Setup

1. **Clone and navigate to the project**:
```bash
cd chat-with-db
```

2. **Create virtual environment**:
```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**:
```bash
pip install -r requirements.txt
```

4. **Configure environment**:
```bash
cp .env.production .env
# Edit .env with your actual values
```

## Running the Server

```bash
python server.py
```

The server will start on http://localhost:8000 by default.

## API Endpoints

- `GET /`: Root endpoint with API information
- `GET /health`: Health check endpoint
- `POST /api/chat/ask`: Ask a question to the database
- `POST /api/chat/ask/stream`: Ask a question and stream the response
- `POST /api/chat/generate-sql`: Generate SQL for a question
- `POST /api/chat/execute-sql`: Execute SQL on the database

## Example Usage

```python
import requests

# Ask a question
response = requests.post(
    "http://localhost:8000/api/chat/ask",
    json={
        "question": "How many users registered last month?",
        "db_type": "postgres"
    }
)
print(response.json())
```

## Deployment

### Local Docker

```bash
# Build and run locally
docker build -t chat-with-db .
docker run -p 8000:8080 --env-file .env chat-with-db
```

### Google Cloud Run

For production deployment to Google Cloud Run:

```bash
# Quick deployment
./deploy-cloud-run.sh
```

For detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md).

## Environment Variables

### Required
- `OPENAI_API_KEY`: OpenAI API key for Vanna AI
- `GOOGLE_CLOUD_PROJECT`: Google Cloud project ID (for BigQuery)

### Database Configuration
- `PGHOST`: PostgreSQL host
- `PGDATABASE`: PostgreSQL database name
- `PGUSER`: PostgreSQL username
- `PGPASSWORD`: PostgreSQL password
- `PGPORT`: PostgreSQL port (default: 5432)

### Optional
- `PORT`: Server port (default: 8080 for Cloud Run, 8000 for local)
- `ENVIRONMENT`: Set to "production" for production settings
- `ALLOWED_ORIGINS`: Comma-separated list of allowed CORS origins
- `LOG_LEVEL`: Logging level (default: INFO)

## Architecture

The AI agent uses:
- **Vanna AI**: For natural language to SQL conversion
- **OpenAI GPT-4**: As the underlying language model
- **ChromaDB**: For vector storage and retrieval
- **FastAPI**: For the REST API backend
- **BigQuery/PostgreSQL**: As supported databases

## Testing

```bash
# Run API tests
python test_api.py

# Test BigQuery integration
python test_bigquery_integration.py

# Test Vanna integration
python test_vanna_integration.py
```

## Monitoring

- **Health Check**: `GET /health`
- **API Documentation**: `GET /docs` (Swagger UI)
- **Metrics**: Available in Cloud Run console
- **Logs**: Structured logging with Cloud Logging integration

## Security

- Non-root Docker user
- Environment-based CORS configuration
- Secure error handling (no internal details in production)
- Service account with minimal permissions
- Secret management via environment variables

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
