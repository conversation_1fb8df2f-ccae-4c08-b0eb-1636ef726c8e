#!/usr/bin/env python3

import requests
import json
import time

def test_postgres_api():
    """Test the PostgreSQL API with Vanna AI"""
    base_url = "http://localhost:8000"

    print("🔄 Testing PostgreSQL API with Vanna AI...")

    # Test 1: Check if server is running
    print("\n1. Testing server health...")
    response = requests.get(f"{base_url}/health")
    if response.status_code == 200:
        print("✅ Server is running")
        print(f"Response: {response.json()}")
    else:
        print(f"❌ Server not responding: {response.status_code}")
        return False

    # Test 2: Ask questions (this will automatically connect and train)
    print("\n2. Testing query generation with Vanna AI...")
    test_questions = [
        "How many companies are there?",
        "What are the different permit types?",
        "List the first 5 users with their email addresses",
        "Show me all contractors"
    ]

    for i, question in enumerate(test_questions, 1):
        print(f"\n📝 Question {i}: {question}")
        try:
            response = requests.post(
                f"{base_url}/api/chat/ask",
                json={
                    "question": question,
                    "db_type": "postgres",
                    "stream": False
                },
                timeout=60  # Give it time for training on first request
            )

            if response.status_code == 200:
                result = response.json()
                print("✅ Query successful")
                print(f"SQL: {result.get('sql', 'N/A')}")

                # Check results
                results_data = result.get('results', {})
                if isinstance(results_data, dict) and 'results' in results_data:
                    row_count = len(results_data['results'])
                    print(f"Results: {row_count} rows")
                    if row_count > 0:
                        print(f"Sample data: {results_data['results'][0] if results_data['results'] else 'None'}")
                else:
                    print(f"Results: {results_data}")

                if result.get('explanation'):
                    print(f"Explanation: {result['explanation'][:150]}...")
            else:
                print(f"❌ Query failed: {response.status_code}")
                print(f"Error: {response.text}")

        except requests.exceptions.Timeout:
            print("⏰ Request timed out (this is normal for the first request as it trains the model)")
        except Exception as e:
            print(f"❌ Error: {e}")

    print("\n🎉 API testing completed!")
    return True

if __name__ == "__main__":
    test_postgres_api()
