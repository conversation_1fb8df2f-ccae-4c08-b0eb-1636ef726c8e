#!/usr/bin/env python3

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_bigquery_integration():
    """Test BigQuery Vanna AI integration"""
    try:
        print("🔄 Testing BigQuery Vanna AI integration...")
        
        # Import the BigQuery agent
        from app.agents.bigquery_agent import BigQueryChatAgent
        
        # Create agent instance
        agent = BigQueryChatAgent(
            project_id=os.environ.get("GOOGLE_CLOUD_PROJECT"),
            vanna_model_name="test-bigquery-model",
            vanna_api_key=os.environ.get("OPENAI_API_KEY"),
            cred_file_path=os.environ.get("GOOGLE_APPLICATION_CREDENTIALS")
        )
        
        print("✅ Agent created successfully")
        
        # Test connection
        if agent.connect():
            print("✅ BigQuery connection successful")
        else:
            print("❌ BigQuery connection failed")
            return False
        
        # Test training
        if agent.train():
            print("✅ Agent training successful")
        else:
            print("❌ Agent training failed")
            return False
        
        print("🎉 BigQuery Vanna AI integration test completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

if __name__ == "__main__":
    success = test_bigquery_integration()
    sys.exit(0 if success else 1)
