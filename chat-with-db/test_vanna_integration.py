#!/usr/bin/env python3

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_vanna_integration():
    """Test Vanna AI integration with PostgreSQL"""
    try:
        print("🔄 Testing Vanna AI integration...")
        
        # Import the PostgreSQL agent
        from app.agents.postgres_agent import PostgresChatAgent
        
        # Create agent instance
        agent = PostgresChatAgent(
            host=os.environ.get("PGHOST"),
            dbname=os.environ.get("PGDATABASE"),
            user=os.environ.get("PGUSER"),
            password=os.environ.get("PGPASSWORD"),
            port=int(os.environ.get("PGPORT", "5432")),
            vanna_model_name="test-postgres-model",
            vanna_api_key=os.environ.get("OPENAI_API_KEY")
        )
        
        print("✅ Agent created successfully")
        
        # Test connection
        if agent.connect():
            print("✅ Database connection successful")
        else:
            print("❌ Database connection failed")
            return False
        
        # Test training
        if agent.train():
            print("✅ Agent training successful")
        else:
            print("❌ Agent training failed")
            return False
        
        print("🎉 Vanna AI integration test completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

if __name__ == "__main__":
    success = test_vanna_integration()
    sys.exit(0 if success else 1)
