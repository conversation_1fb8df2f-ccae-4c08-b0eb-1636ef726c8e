# Server configuration
PORT=8000

# LLM API keys (REQUIRED for Vanna AI)
OPENAI_API_KEY=your_openai_api_key_here
# Alternative: GOOGLE_API_KEY=your_google_api_key

# Vanna AI configuration (optional - uses local ChromaDB by default)
# VANNA_MODEL=your_custom_model_name

# BigQuery configuration (if using BigQuery)
GOOGLE_CLOUD_PROJECT=your_gcp_project_id
GOOGLE_APPLICATION_CREDENTIALS=path/to/credentials.json

# PostgreSQL configuration (REQUIRED)
PGHOST=localhost
PGDATABASE=your_database
PGUSER=your_username
PGPASSWORD=your_password
PGPORT=5432
