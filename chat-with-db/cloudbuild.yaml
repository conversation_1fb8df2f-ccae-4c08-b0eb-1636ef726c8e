# Cloud Build configuration for Cha<PERSON> with DB AI Agent
# This file enables automated deployment via Cloud Build

steps:
  # Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/chat-with-db:$COMMIT_SHA',
      '-t', 'gcr.io/$PROJECT_ID/chat-with-db:latest',
      '.'
    ]
    dir: 'chat-with-db'

  # Push the Docker image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/chat-with-db:$COMMIT_SHA']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/chat-with-db:latest']

  # Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args: [
      'run', 'deploy', 'chat-with-db',
      '--image', 'gcr.io/$PROJECT_ID/chat-with-db:$COMMIT_SHA',
      '--region', 'us-central1',
      '--platform', 'managed',
      '--allow-unauthenticated',
      '--memory', '2Gi',
      '--cpu', '2',
      '--timeout', '3600',
      '--concurrency', '80',
      '--max-instances', '10',
      '--service-account', 'chat-with-db@$PROJECT_ID.iam.gserviceaccount.com'
    ]

# Store images in Container Registry
images:
  - 'gcr.io/$PROJECT_ID/chat-with-db:$COMMIT_SHA'
  - 'gcr.io/$PROJECT_ID/chat-with-db:latest'

# Build options
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'

# Timeout for the entire build
timeout: '1200s'
