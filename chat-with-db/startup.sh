#!/bin/bash

# Startup script for Chat with DB AI Agent
# Ensures fresh ChromaDB state on container startup

echo "🚀 Starting Chat with DB AI Agent..."

# Remove any existing ChromaDB files to ensure fresh state
echo "🔄 Ensuring fresh ChromaDB state..."
rm -rf /app/chroma_db_bigquery-model/*
rm -rf /app/chroma_db_postgres-model/*

# Recreate empty directories
mkdir -p /app/chroma_db_bigquery-model
mkdir -p /app/chroma_db_postgres-model

echo "✅ ChromaDB directories reset"

# Start the FastAPI server
echo "🌐 Starting FastAPI server..."
exec uvicorn app.main:app --host 0.0.0.0 --port 8080
