#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to reset ChromaDB databases for BigQuery and PostgreSQL agents
This fixes the "no such column: collections.topic" error
"""

import os
import shutil
import sys

def reset_chromadb():
    """Reset ChromaDB databases by removing existing directories"""
    
    # Directories to reset
    directories = [
        "chroma_db_bigquery-model",
        "chroma_db_postgres-model"
    ]
    
    print("🔄 Resetting ChromaDB databases...")
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"🗑️ Removing {directory}...")
            shutil.rmtree(directory)
            print(f"✅ Removed {directory}")
        else:
            print(f"ℹ️ {directory} does not exist, skipping")
    
    # Recreate empty directories
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 Created empty {directory}")
    
    print("✅ ChromaDB reset complete!")
    print("📝 Next time you run the agent, it will retrain on the database schema")

if __name__ == "__main__":
    reset_chromadb()
