#!/bin/bash

# Monitoring script for Chat with DB AI Agent on Cloud Run
# This script helps monitor the deployed service

set -e

# Configuration
PROJECT_ID="ais-prod-440309"
SERVICE_NAME="chat-with-db"
REGION="us-central1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}📊 Monitoring Chat with DB AI Agent${NC}"

# Function to check service status
check_service_status() {
    echo -e "${YELLOW}🔍 Checking service status...${NC}"
    
    # Get service details
    SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format='value(status.url)' 2>/dev/null || echo "")
    
    if [ -z "$SERVICE_URL" ]; then
        echo -e "${RED}❌ Service not found or not deployed${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ Service URL: ${SERVICE_URL}${NC}"
    
    # Check health endpoint
    echo -e "${YELLOW}🏥 Checking health endpoint...${NC}"
    if curl -s -f "${SERVICE_URL}/health" > /dev/null; then
        echo -e "${GREEN}✅ Health check passed${NC}"
    else
        echo -e "${RED}❌ Health check failed${NC}"
    fi
    
    # Test API endpoint
    echo -e "${YELLOW}🧪 Testing API endpoint...${NC}"
    RESPONSE=$(curl -s -X POST "${SERVICE_URL}/api/chat/ask" \
        -H "Content-Type: application/json" \
        -d '{"question": "SELECT 1 as test", "db_type": "bigquery"}' || echo "ERROR")
    
    if [[ "$RESPONSE" == *"ERROR"* ]]; then
        echo -e "${RED}❌ API test failed${NC}"
    else
        echo -e "${GREEN}✅ API test passed${NC}"
    fi
}

# Function to show service metrics
show_metrics() {
    echo -e "${YELLOW}📈 Service metrics (last 1 hour):${NC}"
    
    # Get service revision
    REVISION=$(gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format='value(status.latestReadyRevisionName)')
    
    echo -e "${BLUE}📊 Request count:${NC}"
    gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME} AND httpRequest.requestMethod!=OPTIONS" --limit=100 --format="table(timestamp,httpRequest.requestMethod,httpRequest.requestUrl,httpRequest.status)" --freshness=1h
    
    echo -e "${BLUE}🚨 Error logs:${NC}"
    gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME} AND severity>=ERROR" --limit=10 --format="table(timestamp,severity,textPayload)" --freshness=1h
}

# Function to show resource usage
show_resources() {
    echo -e "${YELLOW}💾 Resource configuration:${NC}"
    gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format="table(
        spec.template.spec.containers[0].resources.limits.memory:label=MEMORY,
        spec.template.spec.containers[0].resources.limits.cpu:label=CPU,
        spec.template.spec.containers[0].env[].name:label=ENV_VARS
    )"
}

# Function to tail logs
tail_logs() {
    echo -e "${YELLOW}📜 Tailing logs (Ctrl+C to stop):${NC}"
    gcloud logs tail "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME}" --format="table(timestamp,severity,textPayload)"
}

# Main menu
show_menu() {
    echo -e "${BLUE}Choose an option:${NC}"
    echo "1. Check service status"
    echo "2. Show metrics"
    echo "3. Show resource configuration"
    echo "4. Tail logs"
    echo "5. All checks"
    echo "6. Exit"
}

# Main loop
while true; do
    echo ""
    show_menu
    read -p "Enter your choice (1-6): " choice
    
    case $choice in
        1)
            check_service_status
            ;;
        2)
            show_metrics
            ;;
        3)
            show_resources
            ;;
        4)
            tail_logs
            ;;
        5)
            check_service_status
            echo ""
            show_metrics
            echo ""
            show_resources
            ;;
        6)
            echo -e "${GREEN}👋 Goodbye!${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ Invalid option. Please choose 1-6.${NC}"
            ;;
    esac
done
