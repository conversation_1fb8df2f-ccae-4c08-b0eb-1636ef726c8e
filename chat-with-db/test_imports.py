#!/usr/bin/env python3
"""
Test script to verify all imports work correctly
"""

import sys
import os

def test_imports():
    """Test all critical imports"""
    print("🧪 Testing imports...")
    
    try:
        print("📦 Testing FastAPI...")
        import fastapi
        print(f"✅ FastAPI {fastapi.__version__}")
    except ImportError as e:
        print(f"❌ FastAPI import failed: {e}")
        return False
    
    try:
        print("📦 Testing Uvicorn...")
        import uvicorn
        print(f"✅ Uvicorn {uvicorn.__version__}")
    except ImportError as e:
        print(f"❌ Uvicorn import failed: {e}")
        return False
    
    try:
        print("📦 Testing OpenAI...")
        import openai
        print(f"✅ OpenAI {openai.__version__}")
    except ImportError as e:
        print(f"❌ OpenAI import failed: {e}")
        return False
    
    try:
        print("📦 Testing Google Cloud BigQuery...")
        from google.cloud import bigquery
        print("✅ Google Cloud BigQuery")
    except ImportError as e:
        print(f"❌ Google Cloud BigQuery import failed: {e}")
        return False
    
    try:
        print("📦 Testing ChromaDB...")
        import chromadb
        print(f"✅ ChromaDB {chromadb.__version__}")
    except ImportError as e:
        print(f"❌ ChromaDB import failed: {e}")
        return False
    
    try:
        print("📦 Testing Vanna AI...")
        import vanna
        print("✅ Vanna AI")
    except ImportError as e:
        print(f"❌ Vanna AI import failed: {e}")
        return False
    
    try:
        print("📦 Testing Vanna ChromaDB integration...")
        from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
        print("✅ Vanna ChromaDB integration")
    except ImportError as e:
        print(f"❌ Vanna ChromaDB integration failed: {e}")
        return False
    
    try:
        print("📦 Testing Vanna OpenAI integration...")
        from vanna.openai.openai_chat import OpenAI_Chat
        print("✅ Vanna OpenAI integration")
    except ImportError as e:
        print(f"❌ Vanna OpenAI integration failed: {e}")
        return False
    
    try:
        print("📦 Testing application imports...")
        from app.main import app
        print("✅ Application imports")
    except ImportError as e:
        print(f"❌ Application import failed: {e}")
        return False
    
    print("🎉 All imports successful!")
    return True

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
