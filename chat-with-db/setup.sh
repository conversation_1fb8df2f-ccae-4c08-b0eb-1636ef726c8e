#!/bin/bash

# Setup script for Chat with DB AI Agent
# This script helps set up the development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Setting up Chat with DB AI Agent${NC}"

# Check Python version
echo -e "${YELLOW}🐍 Checking Python version...${NC}"
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    echo -e "${GREEN}✅ Python ${PYTHON_VERSION} found${NC}"
else
    echo -e "${RED}❌ Python 3 not found. Please install Python 3.8+${NC}"
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}📦 Creating virtual environment...${NC}"
    python3 -m venv venv
    echo -e "${GREEN}✅ Virtual environment created${NC}"
else
    echo -e "${BLUE}📦 Virtual environment already exists${NC}"
fi

# Activate virtual environment
echo -e "${YELLOW}🔄 Activating virtual environment...${NC}"
source venv/bin/activate

# Upgrade pip
echo -e "${YELLOW}⬆️ Upgrading pip...${NC}"
pip install --upgrade pip

# Install dependencies
echo -e "${YELLOW}📚 Installing dependencies...${NC}"
pip install -r requirements.txt

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}⚙️ Creating .env file...${NC}"
    cp .env.production .env
    echo -e "${GREEN}✅ .env file created from template${NC}"
    echo -e "${YELLOW}📝 Please edit .env file with your actual values${NC}"
else
    echo -e "${BLUE}⚙️ .env file already exists${NC}"
fi

# Create necessary directories
echo -e "${YELLOW}📁 Creating necessary directories...${NC}"
mkdir -p chroma_db_bigquery-model
mkdir -p chroma_db_postgres-model
echo -e "${GREEN}✅ Directories created${NC}"

# Test installation
echo -e "${YELLOW}🧪 Testing installation...${NC}"
python -c "import fastapi, vanna, uvicorn; print('All dependencies imported successfully')"
echo -e "${GREEN}✅ Installation test passed${NC}"

echo -e "${GREEN}🎉 Setup completed successfully!${NC}"
echo -e "${YELLOW}📝 Next steps:${NC}"
echo "1. Edit .env file with your API keys and database credentials"
echo "2. Run the server: python server.py"
echo "3. Test the API: python test_api.py"
echo "4. View API docs: http://localhost:8000/docs"

echo -e "${BLUE}💡 Useful commands:${NC}"
echo "  Start server: python server.py"
echo "  Run tests: python test_api.py"
echo "  Build Docker: docker build -t chat-with-db ."
echo "  Deploy to Cloud Run: ./deploy-cloud-run.sh"
