# Production Environment Configuration for Cloud Run
# Copy this file to .env.production and update with your production values

# Server configuration
PORT=8080

# LLM API keys (REQUIRED for Vanna AI)
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# BigQuery configuration
GOOGLE_CLOUD_PROJECT=your_project_id_here
# For Cloud Run, use service account attached to the service instead of credentials file
# GOOGLE_APPLICATION_CREDENTIALS=bigquery-service-account.json

# PostgreSQL configuration (if using external PostgreSQL)
PGHOST=your_postgres_host_here
PGPORT=5432
PGUSER=your_postgres_user_here
PGPASSWORD=your_postgres_password_here
PGDATABASE=your_postgres_database_here

# Production settings
ENVIRONMENT=production
LOG_LEVEL=INFO

# CORS settings for production (restrict to your frontend domains)
ALLOWED_ORIGINS=https://your-frontend-domain.com,https://your-app-domain.com
